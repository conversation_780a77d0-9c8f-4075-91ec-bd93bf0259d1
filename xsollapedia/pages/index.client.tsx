/* eslint-disable react/prop-types */
import FooterV3 from 'components/FooterV3';
import {type FormProps} from 'components/Form';
import {GridGuideLocal} from 'components/GridGuides';
import {DefaultHeader} from 'components/HeaderV3/DefaultHeader/DefaultHeader';
import {Meta} from 'components/Meta';
import {useRouter} from 'next/router';
import {useEffect, useState} from 'react';
import styled from 'styled-components';
import {FormSection} from 'xsollapedia/sections/FormSection';
import {TaxAndLegal} from 'xsollapedia/sections/TaxAndLegal';
import {LeftFloating} from '../components/LeftFloating';
import {RightFloating} from '../components/RightFloating';
import {FormData} from '../components/RightFloating/partials/FormData';
import {PopupProvider} from '../components/RightFloating/partials/PopupContext';
import {Hero, type HeroProps} from '../sections/Hero';
import {Overview} from '../sections/Overview';
import {Payments} from '../sections/Payments';
import {VideoGameIndustry} from '../sections/VideoGameIndustry';
import {initCountryFlagPolyfill} from '../utils/countryFlagPolyfill';

type HomeProps = {
  meta: string | undefined;
  commons: string | undefined;
  hero: HeroProps | undefined;
  form: FormProps | undefined;
  builRedirect?: string | boolean | undefined;
};

const DEFAULT_COUNTRY = '';

export const Home = ({meta, commons, hero, form, builRedirect}: HomeProps) => {
  const router = useRouter();
  const [data, setData] = useState({});

  useEffect(() => {
    if (builRedirect) {
      const win: Window = window;
      win.location = builRedirect;
    }
  }, [builRedirect]);

  const getSlugByIp = async () => {
    const res = await fetch('https://speed.cloudflare.com/meta');
    const meta = await res.json();
    const countryCode = meta.country || '';

    if (!countryCode || !Array.isArray(hero?.locations)) return DEFAULT_COUNTRY;

    const match = hero.locations.find(
      loc => loc.countryCode.toLowerCase() === countryCode.toLowerCase()
    );
    if (match?.countryUrlValue) {
      return match.countryUrlValue.toLowerCase().replaceAll(' ', '-');
    }

    return DEFAULT_COUNTRY;
  };

  useEffect(() => {
    (async () => {
      const {slug} = router.query;
      let countrySlug = slug as string;

      if (router.pathname === '/world-map' && !slug) {
        countrySlug = await getSlugByIp();
      }

      fetchCountryData(countrySlug as string);
      if (!localStorage.getItem('counter')) {
        localStorage.setItem('counter', '0');
      }
    })();
  }, [router.query.slug, router.pathname]);

  useEffect(() => {
    initCountryFlagPolyfill();
  }, []);

  if (builRedirect) return <></>;

  const fetchCountryData = async (country: string, isFallback = false, scrollToInfo = false) => {
    try {
      const {slug} = router.query;
      let routerOption = {scroll: false, shallow: false};
      if (slug) {
        routerOption = {shallow: true, scroll: false};
      }

      const params = new URLSearchParams(window.location.search);
      const countryRouteUrl = new URL(
        `/world-map/${country}${window.location.search}`,
        window.location.origin
      );
      // countryRouteUrl.search = params.toString();

      router.replace(countryRouteUrl, undefined, routerOption);

      const res = await fetch(`/api/cms/countryData?country=${encodeURIComponent(country)}`);
      if (!res.ok) {
        throw new Error(`Failed to fetch data for country: ${country}`);
      }

      const data = await res.json();
      if (!data.data || data.data.length === 0) {
        throw new Error(`No data found for country: ${country}`);
      }

      if (scrollToInfo) {
        scrollToinfoSection();
      }

      setData(data.data[0].attributes);

      const countryCode = data.data[0].attributes.block1.countryCode || '';
      trackUserCountryVisit(countryCode);
    } catch (error) {
      console.error(error.message);
      if (!isFallback) {
        fetchCountryData(DEFAULT_COUNTRY, true);
      }
    }
  };

  function trackUserCountryVisit(country: string) {
    if (country === '') {
      console.error('Country is empty');
      return;
    }

    const key = 'visitedCountries';

    try {
      const visited = JSON.parse(localStorage.getItem(key)) || [];

      if (!visited.includes(country)) {
        visited.push(country);
        localStorage.setItem(key, JSON.stringify(visited));

        const event = new CustomEvent('visitedCountriesUpdated', {
          detail: {country, visitedCountries: visited}
        });
        window.dispatchEvent(event);
      }
    } catch (error) {
      console.error('Failed to update visited countries:', error);
    }
  }

  const scrollToinfoSection = () => {
    const infoSection = document.getElementById('info-section');
    if (infoSection) {
      infoSection.scrollIntoView({behavior: 'smooth'});
    } else {
      setTimeout(scrollToinfoSection, 500);
    }
  };

  return (
    <MapContainer>
      <PopupProvider>
        <Meta {...meta} />
        <DefaultHeader />
        <GridGuideLocal />
        <>
          {data && (
            <>
              <Hero
                {...hero}
                queryCountry={router.query.slug}
                onClick={(country: string) => fetchCountryData(country, false, true)}
              />
              <Overview data={data.block1} common={commons} />
              <VideoGameIndustry data={data.block2} common={commons} />
              <Payments data={data.block3} common={commons.payments} />
              <TaxAndLegal
                commonTax={commons.taxes}
                commonLegal={commons.legal}
                taxData={data.block4}
                legalData={data.block5}
              />
              {data.block1 && <FormSection {...form} common={commons.form} data={data} />}
            </>
          )}
        </>
        {data.block1 && (
          <>
            <RightFloating {...FormData} data={data} />
            <LeftFloating />
          </>
        )}
        <FooterV3 hasMarginTop={false} variant="dark" />
      </PopupProvider>
    </MapContainer>
  );
};

const MapContainer = styled.div`
  font-family: 'Twemoji Country Flags', var(--font-stack);
`;
