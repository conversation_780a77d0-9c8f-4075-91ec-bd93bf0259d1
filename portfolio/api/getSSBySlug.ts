import axios from 'axios';
import {type AllowedLocals} from 'languages/constants';
import {extractData} from 'utils/data/extractData';
import qs from 'qs';
import {type StrapiSuccessStory} from 'strapi/api/success-story/StrapiSuccessStory';
import {config} from 'utils/config';
import {type StrapiHeader} from 'strapi/api/header/StrapiHeader';
import {type StrapiFooter} from 'strapi/api/footer/StrapiFooter';
import {type StrapiSuccessStoryHome} from 'strapi/api/success-story-home/StrapiSuccessStoryHome';
import {type StrapiGlobalConfig} from 'strapi/api/global-config/StrapiGlobalConfig';

export const getSuccessStory = async (
  slug: string,
  locale: AllowedLocals
): Promise<SuccessStoryStrapiProps> => {
  const filterInProd = config.appEnv === 'production' ? {production: '1'} : {};
  const queryString = qs.stringify({
    locale,
    slug,
    ...filterInProd
  });
  return await axios
    .get(`${config.strapiBaseUrl}/api/custom/success-stories?${queryString}`)
    .then(res => res.data)
    .then(extractData);
};

export type SuccessStoryStrapiProps = {
  locale: string;
  availableLocales: AllowedLocals[];
  dataBody: {
    navigation: {
      header: StrapiHeader;
      footer: StrapiFooter;
    };
    shared: StrapiSuccessStoryHome;
    content: StrapiSuccessStory;
    globalConfig: StrapiGlobalConfig;
  };
};
