import styled from 'styled-components';
import {GridGuideLocal} from 'components/GridGuides';
import {DefaultHeader} from 'components/HeaderV2/DefaultHeader/DefaultHeader';
import Footer from 'components/Footer';
import {PortfolioShowcases} from 'portfolio/components/PortfolioShowcases';
import {FormSection} from 'portfolio/components/FormSection';
import {SolutionsUsed} from 'portfolio/components/SolutionsUsed';
import {map} from 'remeda';
import {Hero} from 'portfolio/components/Hero/Hero';
import {Result} from 'portfolio/components/Result';
import type {PageProps} from './inner.server';
import dynamic from 'next/dynamic';
import {config} from 'utils/config';
import {Meta} from 'components/Meta';
import {ReadyToGetStarted} from 'library/ReadyToGetStarted';
import {FUllVideoBlock} from 'library/FullVideoBlock';
import {gridWidth} from 'components/GridGuides';
import {reMediaQuery} from 'utils/reMediaQuery';
import {CommonSectionContainer} from 'components/CommonSectionContainer';

const RevalidationHelper = dynamic(() =>
  import('components/RevalidationHelper').then(mod => mod.RevalidationHelper)
);

const Layout = styled.div``;

const VideoBlockWrapper = styled(CommonSectionContainer)`
  ${reMediaQuery({
    'max-width': ['100%', null, null, null, gridWidth()]
  })}
`;

export const PortfolioInner = ({
  hero,
  ask,
  result,
  portfolioShowcases,
  ebookData,
  contactUsData,
  slug,
  readyTo,
  videoBlock,
  meta
}: PageProps) => {
  return (
    <>
      <Meta {...meta} />
      {config.appEnv !== 'production' && <RevalidationHelper pageUrl={`portfolio/${slug}`} />}
      <GridGuideLocal />
      <Layout>
        <DefaultHeader />
        <Hero {...hero} />
        {videoBlock?.video && (
          <VideoBlockWrapper>
            <FUllVideoBlock {...videoBlock} />
          </VideoBlockWrapper>
        )}
        <SolutionsUsed
          title={ask.title}
          label={ask.label}
          previewImage={ask.image}
          articles={[
            {
              title: ask.challengeLabel,
              text: ask.challenge
            },
            {
              title: ask.solutionLabel,
              text: ask.solution
            }
          ]}
          bottom={{
            title: ask.solutionsUsedLabel,
            solutions: map(ask.solutionsUsed, solution => ({
              text: solution.name,
              icon: solution.icon,
              link: solution.url,
              slug: solution.slug
            }))
          }}
        />
        <Result
          title={result.title}
          label={result.label}
          resultInNumbersLabel={result.resultInNumbersLabel}
          leftContent={result.leftSide}
          rightContent={result.rightSide}
        />
        {ebookData && <FormSection {...ebookData} />}
        {portfolioShowcases && (
          <PortfolioShowcases
            label={portfolioShowcases.label}
            title={portfolioShowcases.title}
            button={{
              text: portfolioShowcases.buttonText,
              url: portfolioShowcases?.buttonURL || '/partner-spotlight' //TODO delete fallback and update styles
            }}
            storyCards={map(portfolioShowcases.successStories, story => ({
              id: story.id,
              backgroundImage: story.backgroundImage,
              backgroundImageBlur: story.backgroundImageBlur,
              companyLogo: story.logo,
              title: story.title,
              productIcons: map(story.solutionsUsed, solution => ({
                id: solution.id,
                src: solution.icon,
                alt: solution.name,
                slug: solution.slug
              })),
              learnMoreLink: {
                text: portfolioShowcases.learnMoreLabel,
                url: story.slug
              },
              demoLink: {
                text: portfolioShowcases.watchDemoLabel,
                url: story.demoUrl
              }
            }))}
          />
        )}
        {contactUsData && <FormSection {...contactUsData} />}

        {readyTo && <ReadyToGetStarted {...readyTo} />}
        <Footer />
      </Layout>
    </>
  );
};
