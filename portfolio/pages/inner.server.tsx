import {type GetServerSidePropsContext, type GetStaticPaths} from 'next';
import {getMessages} from 'languages/helpers/getMessages';
import {notFound, props} from 'utils/getStaticProps';
import {getSuccessStory} from '../api/getSSBySlug';
import {isLocaleAllowed} from '../../languages/helpers';
import {mapStrapiMedia} from 'utils/data/mapStrapiMedia';
import {map, pipe} from 'remeda';
import {type StrapiSuccessStory} from 'strapi/api/success-story/StrapiSuccessStory';
import {type StrapiSolution} from 'strapi/api/solution/StrapiSolution';
import {mapStrapiLogo} from 'components/PartnerLogo/utils/mapStrapiLogo';
import {handleError} from 'utils/handleError';
import {type Logo} from 'components/PartnerLogo/types';
import {type StrapiLogo} from 'strapi/api/logo/StrapiLogo';
import {inlineSvg} from 'utils/inlineSvg';
import {config} from 'utils/config';
import {mapStrapiMeta} from 'utils/data/mapStrapiMeta';
import {type Vector5} from 'utils/vector5';
import {staticAdapter as mapFormSection} from 'library/FormSection/staticAdapter';
import {sentryLogger} from 'utils/sentryLogger';
import {localeRedirect} from 'page-constructor/getStaticProps';
import {isAxiosError} from 'axios';
import {adaptFooterProps} from 'components/Footer/data/adapter';
import {adaptHeaderProps} from 'components/Header/data/adapter';
import {adaptHeaderProps as adaptHeaderV2Props} from 'components/HeaderV2/data/adapter';
import {type StrapiButton} from 'strapi/components/link/StrapiButton';
import {mapStrapiBlogBlurMedia} from 'blog/pages/common.server';
import {type StrapiImageIllustrationV3} from 'strapi/components/illustration/StrapiImageIllustrationV3';
import {type StrapiMedia} from 'strapi/StrapiMedia';
import {
  type BreakpointSettings,
  type ImageIllustrationProps
} from 'portfolio/components/Hero/types';
import {staticAdapter as mapReadyTo} from 'library/ReadyToGetStarted/staticAdapter';
import {mapVideo} from 'components/Video/video.server';
import {type StrapiBreakpointValues} from 'strapi/components/block-c/StrapiBreakpointValues';
import {type UnitLessValues} from 'utils/reMediaQuery';
import {mapCommonSectionSettings} from 'utils/data/mapCommonSectionSettings';

export type PageProps = Extract<
  Awaited<ReturnType<typeof getStaticProps>>,
  {props: unknown}
>['props'];
const portfolioSlug = 'partner-spotlight';
const capitalize = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const getStaticPaths: GetStaticPaths<{portfolioSlug: string}> = async () => {
  // const successStories = await getSSList(locales.en);
  // return {
  //   paths: map(successStories, ss => ({
  //     params: {
  //       portfolioSlug: ss.ssSlug
  //     }
  //   })),
  //   fallback: 'blocking'
  // };

  return {
    paths: [],
    fallback: 'blocking'
  };
};

const applyIfDefined = <T, R>(value: T | undefined | null, fn: (value: T) => R) => {
  if (value === undefined) return undefined;
  if (value === null) return undefined;
  return fn(value);
};

export const getStaticProps = async (
  nextJsContext: GetServerSidePropsContext<{portfolioSlug: string}>
  // eslint-disable-next-line sonarjs/cognitive-complexity
) => {
  const {locale = 'en', params} = nextJsContext;

  const {portfolioSlug} = params || {};

  if (!portfolioSlug) return notFound();
  const logger = sentryLogger('Success Story', portfolioSlug, locale);

  if (!isLocaleAllowed(locale)) return notFound();

  const [result, resultError] = await handleError(getSuccessStory(portfolioSlug, locale));
  if (resultError) {
    if (isAxiosError(resultError) && resultError.response?.status !== 404) {
      logger.error(`getEventBookingBySlug : ${resultError.message}`);
      throw resultError;
    }
    logger.error('Failed to fetch page data');
    return notFound();
  }
  const {availableLocales, dataBody} = result;

  const redirectToDifferentLocal = localeRedirect(
    availableLocales,
    locale,
    `portfolio/${portfolioSlug}`,
    logger
  );

  if (redirectToDifferentLocal) return redirectToDifferentLocal;

  if (dataBody == undefined) {
    return notFound();
  }
  const successStory = dataBody.content;
  const successStoryHome = dataBody.shared;
  const meta = mapStrapiMeta(`${config.baseUrl}/partner-spotlight/${portfolioSlug}`)(
    successStory.ssMeta
  );

  return props({
    readyTo: dataBody.globalConfig.gcFooterCta
      ? mapReadyTo(dataBody.globalConfig.gcFooterCta)
      : undefined,
    slug: portfolioSlug,
    meta,
    hero: await mapHeroSection(successStory),
    ask: await mapAskSection(successStory),
    result: await mapResultSection(successStory),
    portfolioShowcases: successStory.ssShowFeaturedStories
      ? {
          title: successStoryHome.ssmSsWcircleTitle,
          label: successStoryHome.ssmSsWcircleLabel,
          buttonText: successStoryHome.ssmSeeAllStoriesText,
          learnMoreLabel: successStoryHome.ssmViewStoryText,
          watchDemoLabel: successStoryHome.ssmWatchDemoText,
          successStories: await Promise.all(
            map(successStory.ssFeaturedStories || [], mapSuccessStoryCard)
          )
        }
      : undefined,
    ebookData: successStory.ssEbookForm ? mapFormSection(successStory.ssEbookForm) : undefined,
    contactUsData: successStory.ssShowContactUs
      ? mapFormSection(
          successStory.ssContactUs ? successStory.ssContactUs : successStoryHome.ssmContactUs
        )
      : undefined,
    videoBlock: await mapVideoBlock(successStory),
    navigation: {
      header: adaptHeaderProps(dataBody.navigation.header),
      headerV2: {
        ...adaptHeaderV2Props(dataBody.navigation.header2),
        headerType: dataBody.content?.ssPageSettings?.headerType,
        headerTheme: dataBody.content?.ssPageSettings?.headerTheme
      },
      footer: adaptFooterProps(dataBody.navigation.footer)
    },
    localization: {
      messages: await getMessages(locale),
      locale: locale,
      availableLocales
    }
  });
};
const mapResultSection = async (data: StrapiSuccessStory) => {
  return {
    label: data.ssResults.label,
    title: data.ssResults.titleLeft,
    resultInNumbersLabel: data.ssResults.titleRight,
    leftSide: data.ssResults.contentLeft,
    rightSide: data.ssResults.contentRight
  };
};
const mapAskSection = async (data: StrapiSuccessStory) => {
  return {
    label: data.ssAsk.label,
    title: data.ssAsk.title,
    image: data.ssAsk.image ? applyIfDefined(data.ssAsk.image, mapStrapiMedia) : undefined,
    solutionLabel: capitalize(data.ssAsk.solutionLabel),
    solution: data.ssAsk.solutionDescription,
    challengeLabel: capitalize(data.ssAsk.challengeLabel),
    challenge: data.ssAsk.challengeDescription,
    solutionsUsedLabel: data.ssAsk.solutionUsedLabel,
    solutionsUsed: await Promise.all(map(data.ssSolutionsUsed || [], mapSoulutionUsed))
  };
};

const mapHeroSection = async (data: StrapiSuccessStory) => {
  return {
    titleUppercase: data.ssHero.titleUppercase ?? 'true',
    backgroundPosition: data.ssHero.backgroundPosition ?? 'center center',
    title: data.ssHero.title,
    description: data.ssHero.description,
    isBig: data.ssHero.bigImage,
    background: [
      mapStrapiMedia(data.ssHero.backgroundImages.bpiMobileS).src,
      mapStrapiMedia(data.ssHero.backgroundImages.bpiMobileM).src,
      mapStrapiMedia(data.ssHero.backgroundImages.bpiTablet).src,
      mapStrapiMedia(data.ssHero.backgroundImages.bpiLaptop).src,
      mapStrapiMedia(data.ssHero.backgroundImages.bpiDesktop).src
    ] as Vector5<string>,
    demoUrl: data.ssDemoUrl,
    logo: await applyIfDefined(data.ssLogo, remapStrapiLogo),
    buttons: pipe(
      data.ssHero.buttons || [],
      map((button: StrapiButton) => {
        return {
          text: button.text ?? '',
          url: button.url ?? '',
          type: button.type ?? '',
          uniqueID: button.uniqueId ?? ''
        };
      })
    ),
    mobileBackgroundOpacity: data.ssHero.mobileBackgroundOpacity,
    illustration: data.ssHero.illustrationV3
      ? mapStrapiIllustrationV3(data.ssHero.illustrationV3)
      : undefined
  };
};

export const mapSoulutionUsed = async (item: StrapiSolution) => {
  return {
    id: item.id,
    icon: await inlineSvg(mapStrapiMedia(item.sIcon).src),
    name: item.sName,
    slug: item.sProductPage?.slug,
    url:
      item.sProductPage?.pageSettings && item.sProductPage?.pageSettings.showOnProd
        ? `/${item.sProductPage?.slug}`
        : ''
  };
};

export const remapStrapiLogo = async (strapiLogo: StrapiLogo): Promise<Logo> => {
  const logo = mapStrapiLogo(strapiLogo);
  return {
    ...logo,
    src: await inlineSvg(logo.src)
  };
};

export type ServerSuccessStoryCard = Awaited<ReturnType<typeof mapSuccessStoryCard>>;

export const mapSuccessStoryCard = async (successStory: StrapiSuccessStory) => {
  return {
    id: successStory.id,
    title: successStory.ssTitle,
    logo: await applyIfDefined(successStory.ssLogo, remapStrapiLogo),
    slug: `/${portfolioSlug}/${successStory.ssSlug}`,
    demoUrl: successStory.ssDemoUrl,
    backgroundImage: mapStrapiMedia(successStory.ssCardImage),
    backgroundImageBlur: mapStrapiBlogBlurMedia(successStory.ssCardImage),
    solutionsUsed: await Promise.all(map(successStory.ssSolutionsUsed || [], mapSoulutionUsed))
  };
};

const mapStrapiIllustrationV3 = (data: StrapiImageIllustrationV3): ImageIllustrationProps => {
  const breakpoints =
    (data.breakpointsv3 as unknown as Vector5<BreakpointSettings>) ?? defaultBreakpoints;
  return {
    desktop: mapStrapiMedia(data.sourceImageDesktop),
    mobile: mapStrapiMedia(
      data.sourceImageMobile ? data.sourceImageMobile : data.sourceImageDesktop
    ),
    aspectRatio: data.sourceImageDesktop.width / data.sourceImageDesktop.height,
    aspectRatioMobile: data.sourceImageMobile
      ? data.sourceImageMobile.width / data.sourceImageMobile.height
      : data.sourceImageDesktop.width / data.sourceImageDesktop.height,
    breakpoints,
    animationData: data.animationData
      ? mapAnimationData(data.animationData, data.sourceImageDesktop, data, breakpoints)
      : undefined,
    animationDataMobile: data.animationDataMobile
      ? mapAnimationData(
          data.animationDataMobile,
          data.sourceImageMobile ?? data.sourceImageDesktop,
          data,
          breakpoints
        )
      : data.animationData
      ? mapAnimationData(data.animationData, data.sourceImageDesktop, data, breakpoints)
      : undefined
  };
};

const mapAnimationData = (
  animationData: StrapiMedia,
  imageSource: StrapiMedia,
  data: StrapiImageIllustrationV3,
  breakpoints: Vector5<BreakpointSettings>
) => {
  return {
    aspectRatio: imageSource ? imageSource.width / imageSource.height : 1,
    src: animationData?.url,
    type: 'animation',
    breakpoints,
    priority: data.priority,
    loop: data.loop
  };
};

const defaultBreakpoints = [
  {
    width: 350
  },
  {
    width: 400
  },
  {
    width: 500
  },
  {
    width: 550
  },
  {
    width: 600
  }
] as const;

const mapVideoBlock = async (data: StrapiSuccessStory) => {
  return {
    video: data.ssVideoBlock.fvbVideo ? mapVideo(data.ssVideoBlock.fvbVideo) : undefined,
    ...mapCommonSectionSettings(data.ssVideoBlock.commonSectionSettings),
    minHeight: data.ssVideoBlock.minHeight
      ? mapMinHeight(data.ssVideoBlock.minHeight)
      : [202, 385, 547, 709, 891],
    aspectRatio: data.ssVideoBlock.aspectRatio,
    loop: !!data.ssVideoBlock.loop
  };
};
const mapMinHeight = (data: StrapiBreakpointValues): UnitLessValues => [
  !data.mobileS ? 'auto' : `${data.mobileS}px`,
  !data.mobileM ? 'auto' : `${data.mobileM}px`,
  !data.tablet ? 'auto' : `${data.tablet}px`,
  !data.laptop ? 'auto' : `${data.laptop}px`,
  !data.desktop ? 'auto' : `${data.desktop}px`
];
