import {SectionLabel} from 'components/SectionLabel';
import {SectionTitle} from 'components/SectionTitle';
import styled from 'styled-components';
import {breakpoints} from 'styles/sizes';
import {type UnitLessValues, reMediaQuery} from 'utils/reMediaQuery';
import {type SolutionsUsedProps} from './types';
import {Article} from './partials/Article';
import {Bottom} from './partials/Bottom';
import Image from 'next/image';
import {col, gridOffset, gridWidth} from 'components/GridGuides';
import {useIsMobile} from 'hooks/useIsMobile';
import {VariantToBackgroundColor} from 'utils/variants';
import {commonSectionMargin} from 'styles/commonSectionMargin';
import {LinkWithIcon} from 'components/LinkWithIcon';

export const SolutionsUsed = ({
  label,
  title,
  previewImage,
  articles,
  bottom
}: SolutionsUsedProps) => {
  const isMobile = useIsMobile();

  return (
    <Container>
      <SectionLabel text={label} />
      <ContentWrapper>
        <Left>
          <SectionTitle>{title}</SectionTitle>
          {previewImage && (
            <ImageWrapper>
              <Image
                src={previewImage.src}
                alt={previewImage.alt}
                layout="fill"
                objectFit="cover"
              />
            </ImageWrapper>
          )}
        </Left>
        {!isMobile && (
          <Center>
            <Divider />
          </Center>
        )}
        <Right>
          {articles.map(article => (
            <Article key={article.title} {...article} />
          ))}
        </Right>
        {isMobile && (
          <Center>
            <Divider />
          </Center>
        )}
      </ContentWrapper>

      <Bottom {...bottom} />
    </Container>
  );
};

const linkWrapperGap = [10, 10, 10, 10, 15];
const Container = styled.section`
  margin: 0 auto;
  background: ${VariantToBackgroundColor['blue']};
  ${commonSectionMargin()}
  ${reMediaQuery({
    'padding-top': [40, 51, 55, 80, 100],
    'padding-left': [gridOffset(), null, null, null, col()],
    'padding-right': [gridOffset(), null, null, null, col()],
    'padding-bottom': [60, 60, 80, 100, 140].map(
      (item, idx) => item - linkWrapperGap[idx]
    ) as UnitLessValues,
    'max-width': [`100%`, null, null, null, gridWidth()]
  })}

  ${SectionLabel} {
    ${reMediaQuery({
      'margin-bottom': [25, 45, 45, 65, 80]
    })}
  }

  ${SectionTitle} {
    ${reMediaQuery({
      'margin-bottom': [50, 35, 35, 65, 80]
    })}
  }

  ${LinkWithIcon} {
    ${reMediaQuery({
      'margin-right': linkWrapperGap as UnitLessValues,
      'margin-bottom': linkWrapperGap as UnitLessValues
    })}
  }
`;

const ContentWrapper = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: ${col(9)} ${col(1)} ${col(6)};

  ${reMediaQuery({
    'margin-bottom': [50, 45, 45, 65, 80],
    'grid-template-columns': [
      '1fr',
      `${col(9)} ${col(1)} ${col(6)}`,
      `${col(9)} ${col(1)} ${col(6)}`,
      `${col(9)} ${col(1)} ${col(6)}`,
      `${col(9)} ${col(1)} ${col(6)}`
    ]
  })}

  @media (max-width: ${breakpoints.mobileM}) {
    grid-auto-flow: row;
    gap: 50px;
    border-bottom: 1px solid rgba(24, 23, 28, 0.15);
  }
`;
const Left = styled.div``;
const Center = styled.div`
  position: relative;
`;
const Right = styled.div`
  display: grid;

  ${reMediaQuery({
    gap: [65, 35, 45, 66, 80]
  })}
`;

const Divider = styled.hr`
  position: absolute;
  height: 100%;
  border: none;
  border-left: 1px solid;
  color: ${({theme}) => theme.colors.core.divider.divider};
  left: 50%;
  top: 0;
  bottom: 0;

  @media (max-width: ${breakpoints.mobileM}) {
    width: 100%;
    left: 0;
    height: 0;
    bottom: 0;
    top: unset;
  }
`;

const widths = [192, 120, 142, 160, 208];
const aspectRatio = 1.78;
const heights = widths.map(width => width / aspectRatio);

const ImageWrapper = styled.div`
  overflow: hidden;
  position: relative;

  ${reMediaQuery({
    width: widths as UnitLessValues,
    height: heights as UnitLessValues
  })}

  img {
    border-radius: 6px;
  }
`;
