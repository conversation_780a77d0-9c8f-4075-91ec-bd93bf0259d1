import {H4, <PERSON>, Paragraph} from 'design';
import React, {type ReactNode} from 'react';
import styled from 'styled-components';
import {reMediaQuery} from 'utils/reMediaQuery';
import {type ArticleProps} from './types';
import {ReactMarkdownParser} from 'portfolio/components/Result/ReactMarkdownParser';
import {type ReactMarkdownOptions} from 'react-markdown/lib/react-markdown';

const ArticleMarkdownComponents: Exclude<ReactMarkdownOptions['components'], undefined> = {
  a: ({children, href}: {children: ReactNode; href?: string}) => (
    <Link href={href ?? ''}>{children as string}</Link>
  )
};

export const Article = ({title, text}: ArticleProps) => {
  return (
    <Container>
      <H4>{title}</H4>
      <Paragraph>
        <ReactMarkdownParser
          content={text}
          skipHtml={true}
          components={ArticleMarkdownComponents}
        />
      </Paragraph>
    </Container>
  );
};

const Container = styled.div`
  ${H4} {
    ${reMediaQuery({
      'margin-bottom': [30, 20, 30, 30, 40]
    })}
  }
`;
