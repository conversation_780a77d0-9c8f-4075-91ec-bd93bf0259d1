import {LinkWithIcon} from 'components/LinkWithIcon';
import {BodyLAccent} from 'design';
import React from 'react';
import styled from 'styled-components';
import {reMediaQuery} from 'utils/reMediaQuery';
import {type BottomProps} from './types';
import {breakpoints} from 'styles/sizes';
import {useNavigate} from 'components/Navigation/useNavigate';

export const Bottom = ({title, solutions, className}: BottomProps) => {
  const navigate = useNavigate();
  return (
    <Container className={className}>
      <BodyLAccent>{title}</BodyLAccent>
      <LinkWrapper>
        {solutions.map(({text, icon, link, slug}) => (
          <LinkWithIcon
            key={text}
            text={text}
            link={link}
            icon={icon}
            rel="noopener noreferrer"
            onClick={() => navigate(`/${slug}`)}
            // target="_blank"
          />
        ))}
      </LinkWrapper>
    </Container>
  );
};

const Container = styled.div`
  ${BodyLAccent} {
    ${reMediaQuery({
      'margin-bottom': [30, 20, 30, 30, 40]
    })}
  }
`;

const LinkWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  cursor: pointer;

  @media (max-width: ${breakpoints.mobileM}) {
    display: block;
  }
`;
