import {type Logo} from 'components/PartnerLogo/types';
import {type BaseImage} from 'utils/data/mapStrapiMedia';
import {type Variant} from 'utils/variants';
import {type Vector5} from 'utils/vector5';

type ButtonProps = {
  text: string;
  url: string;
  type: string;
  uniqueID: string;
};

export type HeroProps = {
  titleUppercase: boolean;
  backgroundPosition: string;
  isBig: boolean;
  variant?: Variant;
  title: string;
  description: string;
  buttons: ButtonProps[];
  // background: string; //TODO delete
  background: HeroImageProps['background'];
  logo?: Logo;
  illustration?: ImageIllustrationProps;
  mobileBackgroundOpacity: boolean;
};

export type HeroImageProps = {
  isBig: HeroProps['isBig'];
  backgroundPosition: string;
  background: Vector5<string>;
};

export type BreakpointsSettings = [
  BreakpointSettings,
  BreakpointSettings,
  BreakpointSettings,
  BreakpointSettings,
  BreakpointSettings
];
export type BreakpointSettings = {
  minHeight?: number;
  offsetTop?: number;
  offsetBottom?: number;
  offsetLeft?: number;
  offsetRight?: number;
  unit: 'px' | '%';
  width: number;
};
export type ImageIllustrationProps = {
  desktop: BaseImage;
  mobile: BaseImage;
  aspectRatio: number;
  aspectRatioMobile: number;
  breakpoints: BreakpointsSettings;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  animationData?: AnimationProps;
  animationDataMobile?: AnimationProps;
};

export type AnimationProps = {
  type: string;
  src: string;
  loop?: boolean;
  priority?: boolean;
  aspectRatio: number;
  breakpoints: BreakpointsSettings;
  minHeight?: number;
};
