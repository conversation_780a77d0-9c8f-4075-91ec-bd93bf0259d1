import {ScrollLink} from 'components/ScrollLink';
import {BodyM, Button, H1} from 'design';
import {type Variant, VariantToThemes} from 'utils/variants';
import styled from 'styled-components';
import {breakpoints} from 'styles/sizes';
import {reMediaQuery} from 'utils/reMediaQuery';
import {type HeroProps} from './types';
import {PartnerLogo} from 'components/PartnerLogo';
import {col} from 'components/GridGuides';
import {useIsMobile} from 'hooks/useIsMobile';
import {ImageWrapper} from './partials/HeroImage/HeroImageBig';
import {HeroImage} from './partials/HeroImage';
import {CommonSectionContainer} from 'components/CommonSectionContainer';
import {IllustrationOrImage} from './partials/IllustrationOrImage';
import {useHeaderV2Props} from 'components/NavigationContext';
import {useHeaderState} from 'components/HeaderV2/data/HeaderState';
import {useEffect} from 'react';

export const Hero = ({
  logo,
  title,
  description,
  buttons,
  background,
  variant,
  backgroundPosition,
  titleUppercase,
  illustration,
  mobileBackgroundOpacity,
  isBig = false
}: HeroProps) => {
  variant = variant || (isBig ? 'dark' : 'white');

  const isMobile = useIsMobile();
  const {headerType} = useHeaderV2Props();
  const [setMenuTextColor] = useHeaderState(({setMenuTextColor}) => [setMenuTextColor]);

  useEffect(() => {
    setMenuTextColor(VariantToThemes[variant].colors.core.text.primary);
  }, []);

  return (
    <SectionContainer variant={variant} isTransparent={headerType === 'Transparent'}>
      {!isMobile && isBig && (
        <HeroImage isBig={isBig} background={background} backgroundPosition={backgroundPosition} />
      )}
      <Container
        isBig={isBig}
        variant={variant}
        hasIllustration={illustration ? true : false}
        mobileBackgroundOpacity={mobileBackgroundOpacity}
      >
        <ContentLayout>
          {logo && <PartnerLogo heightMultiplier={isMobile ? 1.3 : 2} {...logo} />}
          <Title titleUppercase={titleUppercase}>{title}</Title>
          <BodyM>{description}</BodyM>
          {isMobile && (
            <>
              <HeroImage
                isBig={isBig}
                background={background}
                backgroundPosition={backgroundPosition}
              />
              {illustration && <IllustrationOrImage {...illustration} />}
            </>
          )}

          <ButtonsContainer variant={variant}>{getButtons(buttons)}</ButtonsContainer>
        </ContentLayout>
        {!isMobile && (
          <IllustrationArea>
            {!isBig && (
              <HeroImage
                isBig={isBig}
                background={background}
                backgroundPosition={backgroundPosition}
              />
            )}

            {illustration && <IllustrationOrImage {...illustration} />}
          </IllustrationArea>
        )}
      </Container>
    </SectionContainer>
  );
};

const SectionContainer = styled(CommonSectionContainer)<{isTransparent: boolean}>`
  margin-top: 0;
  --inset: 0px;
  position: relative;

  ${({isTransparent}) =>
    isTransparent &&
    reMediaQuery({
      'padding-top': [0, 84, 100, 90, 120]
    })}
`;

const IllustrationArea = styled.div`
  width: auto;
  height: 100%;
  position: relative;
  @media (max-width: ${breakpoints.mobileM}) {
    display: none;
  }
`;

const Title = styled(H1)<{titleUppercase: boolean}>`
  text-transform: ${p => (p.titleUppercase ? 'uppercase' : 'unset')};
`;

const getButtons = (buttons: HeroProps['buttons']) => {
  return buttons.map(({text, url, type}) => {
    return (
      <Button
        variant={type}
        size={'adaptive'}
        key={url}
        text={text}
        url={url}
        forwardedAs={ScrollLink}
      />
    );
  });
};

const ButtonsContainer = styled.div<{variant: Variant}>`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  > *:not(:last-child) {
    ${reMediaQuery({
      'margin-right': [0, 10, 10, 10, 15],
      'margin-bottom': [10, 10, 0, 0, 0]
    })}
  }

  @media (max-width: ${breakpoints.mobileM}) {
    width: 100%;
    margin-bottom: 30px;
    margin-top: auto;
  }
`;

const ContentLayout = styled.div`
  z-index: 2;
  width: auto;
  padding-left: ${col(1)};

  ${reMediaQuery({
    'margin-left': [0, 42, 26, 90, 108],
    'padding-top': [35, 26, 30, 50, 65],
    'padding-bottom': [0, 70, 80, 110, 130]
  })}

  @media (max-width: ${breakpoints.mobileM}) {
    width: 100%;
    padding-inline: 20px;
  }

  ${H1} {
    ${reMediaQuery({
      'margin-bottom': [22, 29, 28, 30, 40]
    })}

    @media (max-width: ${breakpoints.mobileM}) {
      br {
        display: none;
      }
    }
  }

  ${BodyM} {
    ${reMediaQuery({
      'margin-bottom': [35, 37, 50, 40, 50]
    })}
  }

  ${PartnerLogo} {
    svg * {
      fill: ${({theme}) => theme.colors.core.text.primary};
    }

    ${reMediaQuery({
      'margin-bottom': [12, 18, 21, 30, 30]
    })}
  }
`;

const Container = styled(CommonSectionContainer)<{
  isBig: boolean;
  hasIllustration: boolean;
  mobileBackgroundOpacity: boolean;
}>`
  background-color: transparent;
  z-index: 2;
  margin-top: 0;

  width: unset;
  position: relative;
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: 1fr 1fr;
  max-width: 1800px;

  --inset: 0px;

  @media (max-width: ${breakpoints.mobileM}) {
    grid-template-columns: 1fr;
    grid-auto-flow: row;

    ${ButtonsContainer} {
      margin-top: ${({isBig, hasIllustration}) =>
        hasIllustration ? `0px` : `${isBig ? 155 : 30}px`};
      margin-bottom: ${({isBig}) => `${isBig ? 60 : 0}px`};
    }
  }

  ${IllustrationArea} {
    ${p =>
      !p.isBig &&
      reMediaQuery({
        'margin-right': [0, 42, 26, 90, 108]
      })}
  }

  @media (max-width: ${breakpoints.mobileM}) {
    ${ImageWrapper} {
      ${({mobileBackgroundOpacity}) => mobileBackgroundOpacity && 'opacity:0.5;'}
    }
  }
`;
