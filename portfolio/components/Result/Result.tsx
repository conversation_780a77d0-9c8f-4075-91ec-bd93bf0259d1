import {SectionLabel} from 'components/SectionLabel';
import {BodyLAccent, H2} from 'design';
import {type Variant, VariantToThemes} from 'utils/variants';
import styled, {css} from 'styled-components';
import {reMediaQuery} from 'utils/reMediaQuery';
import {SwitchProvider} from 'xsolla/core';
import {
  H4MarkdownComponent,
  H6MarkdownComponent,
  ParagraphMarkdownComponent,
  ReactMarkdownParser,
  leftMarkdownComponents,
  rightMarkdownComponents
} from './ReactMarkdownParser';
import {col, gridOffset, gridWidth} from 'components/GridGuides';
import {breakpoints} from 'styles/sizes';
import {commonSectionMargin} from 'styles/commonSectionMargin';
import {useLayoutEffect, useRef, useState} from 'react';
import {keys} from 'remeda';
import {FancyBox} from '../FancyBox/FancyBox';

type ResultProps = {
  variant?: Variant;
  className?: string;
  label: string;
  title: string;
  resultInNumbersLabel: string;
  leftContent: string;
  rightContent: string;
};

const ResultInNumbersLabel = styled(BodyLAccent)``;

export const Result = styled(
  ({
    label,
    title,
    leftContent,
    rightContent,
    className,
    variant = 'dark',
    resultInNumbersLabel
  }: ResultProps) => {
    const containerRef = useRef<HTMLDivElement | null>(null);
    const numbersRef = useRef<HTMLDivElement | null>(null);

    const [isSticky, setIsSticky] = useState(true);

    useLayoutEffect(() => {
      if (!(containerRef.current && numbersRef.current)) return;
      const container = containerRef.current;
      const numbers = numbersRef.current;
      const numbersHeight = numbers.clientHeight;
      const containerHeight = container.clientHeight;
      if (numbersHeight >= containerHeight * 0.9) {
        setIsSticky(false);
      } else {
        setIsSticky(true);
      }
    }, []);
    return (
      <SwitchProvider theme={VariantToThemes[variant]}>
        <Container>
          <Label text={label} />
          <Heading>{title}</Heading>
          <ResultContainer className={className} ref={containerRef}>
            <ExplanationContainer>
              <ReactMarkdownParser
                content={leftContent}
                skipHtml={true}
                components={leftMarkdownComponents}
                allowedElements={keys(leftMarkdownComponents)}
              />
            </ExplanationContainer>
            <Divider />
            <ResultInNumbers ref={numbersRef} isSticky={isSticky}>
              <ResultInNumbersLabel>{resultInNumbersLabel}</ResultInNumbersLabel>
              <ReactMarkdownParser
                content={rightContent}
                components={rightMarkdownComponents}
                allowedElements={keys(rightMarkdownComponents)}
              />
            </ResultInNumbers>
          </ResultContainer>
        </Container>
      </SwitchProvider>
    );
  }
)``;

const Container = styled.section`
  margin: 0 auto;
  ${commonSectionMargin()}
  background: ${({theme}) => theme.colors.core.background.primary};
  ${reMediaQuery({
    'padding-top': [40, 51, 55, 80, 100],
    'padding-left': [gridOffset(), null, null, null, col()],
    'padding-right': [gridOffset(), null, null, null, col()],
    'padding-bottom': [60, 60, 80, 100, 140],
    'max-width': [`100%`, null, null, null, gridWidth()]
  })}
`;

const ResultContainer = styled('div')`
  display: grid;
  grid-template-columns: 9fr 1fr 6fr;

  @media (max-width: ${breakpoints.mobileM}) {
    grid-template-columns: 1fr;
    gap: 50px;
  }
`;

const ResultInNumbers = styled.div<{isSticky: boolean}>`
  height: max-content;
  ${p =>
    p.isSticky &&
    css`
      position: sticky;
      top: 100px;
    `}
  h1,
  h2,
  h3 {
    border-left: solid;
    border-color: ${({theme}) => `${theme.colors.control.primary.bg}`};
    ${reMediaQuery({
      'border-width': [3, 3, 3, 4, 4],
      'padding-left': [15, 15, 15, 20, 20],
      'margin-bottom': [7, 5, 5, 5, 5]
    })}
  }

  p {
    ${reMediaQuery({
      'padding-left': [18, 18, 18, 24, 24],
      'margin-bottom': [30, 20, 30, 35, 40]
    })}
    &:last-child {
      margin: 0;
    }
    color: ${({theme}) => theme.colors.core.text.secondary};
  }

  ${ResultInNumbersLabel} {
    ${reMediaQuery({
      'margin-bottom': [30, 20, 30, 35, 40]
    })}
  }
`;

const ExplanationContainer = styled('div')`
  ${H4MarkdownComponent} {
    ${reMediaQuery({
      // 'margin-bottom': [30, 20, 35, 65, 40]
      'margin-bottom': [30, 15, 35, 35, 40]
    })}
  }

  ${H6MarkdownComponent} {
    ${reMediaQuery({
      'margin-bottom': [20, 15, 20, 20, 26]
    })}
  }

  ${ParagraphMarkdownComponent}:not(:has(a)) {
    /* TODO are we going to make margins collapse? */

    /* display: block; */
    /* position: static; */

    clear: none;
    ${reMediaQuery({
      'margin-top': [20, 10, 35, 20, 80],
      'margin-bottom': [35, 35, 35, 45, 80]
    })}

    ${FancyBox}:not(:last-of-type) {
      img {
        /* TODO what sized should there be depending on the screen size? */
        margin-bottom: 20px;
      }
    }
  }
`;

const Label = styled(SectionLabel)`
  ${reMediaQuery({
    // 'margin-bottom': [30, 20, 30, 35, 80]
    'margin-bottom': [23, 45, 45, 62, 80]
  })}
`;

const Heading = styled(H2)`
  ${reMediaQuery({
    'margin-bottom': [50, 36, 35, 65, 80]
  })}
`;

const Divider = styled('hr')`
  width: 1px;
  height: 100%;
  background-color: ${({theme}) => theme.colors.core.divider.divider};
  justify-self: center;
  border: none;

  @media (max-width: ${breakpoints.mobileM}) {
    width: 100%;
    height: 1px;
    align-self: center;
  }
`;
