import {BodyLAccent, BodyMAccent, H1, H2, H3, H4, Label, Link, Paragraph} from 'design';
import {Children, type ReactNode, createContext, isValidElement} from 'react';
import ReactMarkdown from 'react-markdown';
import remarkBreaks from 'remark-breaks';
import styled from 'styled-components';
import {ListContainer, ListItem} from 'components/List';
import {Dot} from 'components/Dot';
import {reMediaQuery} from 'utils/reMediaQuery';
import {FancyBox} from '../FancyBox/FancyBox';
import {type ReactMarkdownOptions} from 'react-markdown/lib/react-markdown';
import {config} from 'utils/config';

const NumericListContext = createContext(false);
export const H1MarkdownComponent = styled(H1).attrs({forwardedAs: 'h1'})`
  margin-top: unset;
`;
export const H2MarkdownComponent = styled(H2).attrs({forwardedAs: 'h2'})``;
export const H3MarkdownComponent = styled(H3).attrs({forwardedAs: 'h3'})``;
export const H4MarkdownComponent = styled(H4).attrs({forwardedAs: 'h4'})``;
export const H5MarkdownComponent = styled(BodyLAccent).attrs({forwardedAs: 'h5'})``;
export const H6MarkdownComponent = styled(BodyMAccent).attrs({forwardedAs: 'h6'})``;
export const ParagraphMarkdownComponent = styled(Paragraph).attrs({forwardedAs: 'p'})`
  &:last-child {
    margin-bottom: 0 !important;
  }
`;
export const ImageBox = styled('div')`
  display: grid;
  grid-template-columns: repeat(${({children}) => Math.min(3, Children.count(children))}, 1fr);
  ${reMediaQuery({
    gap: [10, 12, 15, 20, 30],
    'margin-bottom': [35, 35, 35, 65, 80]
  })}
  &:last-child {
    margin: 0;
  }
`;
export const Image = styled('img')`
  width: 100%;
`;

export const leftMarkdownComponents: Exclude<ReactMarkdownOptions['components'], undefined> = {
  h1: ({children}: {children: ReactNode}) => <H4MarkdownComponent>{children}</H4MarkdownComponent>,
  h2: ({children}: {children: ReactNode}) => <BodyLAccent>{children}</BodyLAccent>,
  img: ({src, alt, className}: {src?: string; alt?: string; className?: string}) => (
    <FancyBox src={src || ''} className={className}>
      <Image src={src} title={alt} key={'test'} />
    </FancyBox>
  ),
  p: ({children}: {children: ReactNode}) => {
    const allChildImage = Children.toArray(children).every(child => {
      if (isValidElement(child)) {
        const {props} = child;
        return props.node.tagName === 'img';
      }
      return false;
    });

    if (allChildImage) {
      return <ImageBox>{children}</ImageBox>;
    }
    return <ParagraphMarkdownComponent>{children}</ParagraphMarkdownComponent>;
  },
  a: ({children, href}: {children: ReactNode; href?: string}) => (
    <Link href={href ?? ''}>{children as string}</Link>
  ),
  ul: ({children}: {children: ReactNode}) => <ListContainer> {children}</ListContainer>,
  ol: ({children}: {children: ReactNode}) => {
    return (
      <NumericListContext.Provider value={true}>
        <ListContainer isNumericList>{children}</ListContainer>
      </NumericListContext.Provider>
    );
  },
  li: ({children, index}: {children: ReactNode; index: number}) => (
    <NumericListContext.Consumer>
      {isNumericList => (
        <ListItem>
          {isNumericList ? <Label>{index + 1}</Label> : <Dot />}
          {children}
        </ListItem>
      )}
    </NumericListContext.Consumer>
  ),
  br: () => <br />,
  u: ({children}: {children: ReactNode}) => <u>{children}</u>,
  s: ({children}: {children: ReactNode}) => <s>{children}</s>
};

export const rightMarkdownComponents: Exclude<ReactMarkdownOptions['components'], undefined> = {
  h1: ({children}: {children: ReactNode}) => <H1MarkdownComponent>{children}</H1MarkdownComponent>,
  h2: ({children}: {children: ReactNode}) => <H3MarkdownComponent>{children}</H3MarkdownComponent>,
  p: ({children}: {children: ReactNode}) => (
    <ParagraphMarkdownComponent>{children}</ParagraphMarkdownComponent>
  )
};

type ReactMarkdownParserProps = {
  content: string;
  className?: string;
} & Omit<ReactMarkdownOptions, 'children'>;

export const ReactMarkdownParser = styled(
  ({content, className, components, ...rest}: ReactMarkdownParserProps) => {
    return (
      <ContentContainer className={className}>
        <ReactMarkdown
          transformImageUri={uri => {
            if (uri.startsWith('/')) {
              return `${config.strapiBaseUrl}/${uri}`;
            }
            if (uri.startsWith('https://storage.googleapis.com')) {
              return uri.replace(
                'https://storage.googleapis.com/strapi-bucket-prod/',
                config.cdnAssetsUrl
              );
            }
            return uri;
          }}
          remarkPlugins={[remarkBreaks]}
          components={{...components}}
          {...rest}
        >
          {content}
        </ReactMarkdown>
      </ContentContainer>
    );
  }
)``;

const ContentContainer = styled('div')`
  ${ListContainer} {
    gap: 0;
    ${reMediaQuery({
      'padding-bottom': [30, 15, 35, 35, 40]
    })}
  }
  p {
    margin-top: 0 !important;
  }
`;
