import styled from 'styled-components';

import {reMediaQuery} from 'utils/reMediaQuery';
import {SectionTitle} from 'components/SectionTitle';
import {CommonSectionContainer} from 'components/CommonSectionContainer';
import {type Variant} from 'utils/variants';
import {Button} from 'design';
import {ScrollLink} from 'components/ScrollLink';
import {commonSectionInlinePadding} from 'styles/commonSectionInliePadding';
import {breakpoints} from 'styles/sizes';
import {type ButtonProps} from 'utils/data/mapButtons';
import Balancer from 'react-wrap-balancer';

const Container = styled(CommonSectionContainer)`
  ${reMediaQuery({
    'padding-top': [0, 35, 35, 55, 80],
    'padding-bottom': [60, 35, 35, 55, 80]
  })}

  display: flex;
  flex-direction: column;
  ${commonSectionInlinePadding()}

  ${SectionTitle} {
    ${reMediaQuery({
      'margin-bottom': [25, 25, 20, 40, 50]
    })}
  }
`;

const ButtonsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;

  @media (max-width: ${breakpoints.tablet}) {
    flex-direction: column;
  }
  > *:not(:last-child) {
    ${reMediaQuery({
      'margin-right': [10, 10, 10, 10, 28],
      'margin-bottom': [10, 10, 15, 15, 15]
    })}
  }
`;

export type ReadyToGetStartedProps = {
  title: string;
  buttons: ButtonProps[];
  variant?: Variant;
};

export const ReadyToGetStarted = ({buttons, title, variant = 'white'}: ReadyToGetStartedProps) => {
  return (
    <Container variant={variant}>
      <SectionTitle>
        <Balancer>{title}</Balancer>
      </SectionTitle>
      <ButtonsContainer>
        {buttons.map(({text, url, type = 'secondary', primary}) => {
          return (
            <Button
              variant={type === 'primary' || primary ? 'primary' : 'secondary'}
              size={'adaptive'}
              key={url}
              text={text}
              url={url}
              forwardedAs={ScrollLink}
            />
          );
        })}
      </ButtonsContainer>
    </Container>
  );
};
