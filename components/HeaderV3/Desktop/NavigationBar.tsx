import {colord} from 'colord';
import {useHeaderV2Props, useHeaderV3Props} from 'components/NavigationContext';
import {PartnerLogo} from 'components/PartnerLogo';
import {type ReactNode, useContext, useEffect, useState} from 'react';
import styled, {css} from 'styled-components';
import {breakpoints, breakpointsNum} from 'styles/sizes';
import {type UnitLessValues, type UnitValues, reMediaQuery} from 'utils/reMediaQuery';
import {VariantToThemes} from 'utils/variants';
import {SwitchProvider} from 'xsolla/core';
import GetStartedButton from '../../../salons/components/Header/components/GetStartedButton';
import {brandDarkV3, brandLightV3} from '../../../xsolla/themes';
import {HeaderContext} from '../../HeaderV2/data/HeaderContext';
import Logo from '../components/Logo';
import {XsollaTitle} from '../components/XsollaTitle';
import DropDowns from './Dropdowns';
import StaticBar from './StaticBar';

export type DesktopNavigationProps = {
  logo?: ReactNode;
  dropdown?: ReactNode;
  desktopMenu?: DesktopMenuProps;
};

export type DesktopMenuProps = {
  paddingLeft?: UnitLessValues | UnitValues | null;
  paddingRight?: UnitLessValues | UnitValues | null;
};

export const DesktopNavigationBar = ({desktopMenu}: DesktopNavigationProps) => {
  const {mobileBreakPoint, screenSize} = useContext(HeaderContext);
  const crucialResolution = mobileBreakPoint
    ? `${mobileBreakPoint}px`
    : `${breakpointsNum.laptop}px`;

  const {headerType} = useHeaderV2Props();

  const [maskHeight, setMaskHeight] = useState(0);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleScroll = () => {
    setMaskHeight(window.scrollY);
  };

  const isTransparent = headerType === 'Transparent';

  return (
    <OuterContainer crucialResolution={crucialResolution}>
      <StaticBar />
      <BasicNavigation
        isTransparent={isTransparent}
        top={maskHeight}
        screenSize={screenSize}
        crucialResolution={crucialResolution}
        desktopMenu={desktopMenu}
      />
      <ContentPositionController isTransparent={isTransparent} />
    </OuterContainer>
  );
};

const BasicNavigation = ({
  isTransparent,
  top,
  screenSize
}: {
  isTransparent: boolean;
  top?: number;
  screenSize?: string;
  desktopMenu?: DesktopMenuProps;
}) => {
  const {eventLogo, salonButton} = useHeaderV2Props();
  const {headerTheme} = useHeaderV3Props();

  const color =
    headerTheme === 'white'
      ? brandLightV3.colors.core.text.primary
      : brandDarkV3.colors.core.text.primary;
  const backgroundColor =
    headerTheme === 'white'
      ? colord(brandLightV3.colors.core.background.primary).alpha(0.9).toRgbString()
      : colord(brandDarkV3.colors.core.background.tertiary).alpha(0.8).toRgbString();
  const borderColor =
    headerTheme === 'white'
      ? brandLightV3.colors.core.border.ghost
      : brandDarkV3.colors.core.border.ghost;

  return (
    <Container
      top={top}
      backgroundColor={backgroundColor}
      borderColor={borderColor}
      isTransparent={isTransparent}
    >
      <SubContainer>
        {/* eslint-disable-next-line react/no-children-prop */}
        <HidableLogo top={top} children={<XsollaTitle color={color} />} />
        {!salonButton && (
          <SwitchProvider theme={VariantToThemes['dark']}>
            <DropContainer>
              <PilledDropDowns
                top={top}
                screenSize={screenSize}
                backgroundColor={backgroundColor}
                borderColor={borderColor}
              >
                <DropDowns color={color} />
              </PilledDropDowns>
            </DropContainer>
          </SwitchProvider>
        )}
        {salonButton && (
          <RightBtn>
            <GetStartedButton
              text={salonButton.label}
              link={salonButton.href}
              type={salonButton.type}
            />
          </RightBtn>
        )}
        {eventLogo && <EventLogo {...eventLogo} />}
      </SubContainer>
    </Container>
  );
};

const HidableLogo = styled(Logo)<{
  top: number;
}>`
  transition: opacity 200ms linear;
  ${props =>
    reMediaQuery({
      opacity: [null, null, null, getLogoHidden(props.top, 54), getLogoHidden(props.top, 71)]
    })};
`;

const EventLogo = styled(PartnerLogo)`
  position: absolute;
  top: 50%;
  transform: translate(0%, -50%);
  ${reMediaQuery({
    right: [null, null, null, 90, 108]
  })}
`;

const RightBtn = styled.div`
  position: absolute !important;
  top: 50%;
  transform: translate(0%, -50%);
  ${reMediaQuery({
    right: [null, null, null, 90, 108]
  })}
`;

const OuterContainer = styled.div<{crucialResolution: string}>`
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 100;
  display: none;
  ${({crucialResolution}) => css`
    @media (min-width: ${crucialResolution}) {
      display: block;
    }
  `}
`;

const Container = styled.div<{
  backgroundColor: string;
  top: number;
  isTransparent: boolean;
}>`
  width: 100%;
  display: flex;
  grid-template-columns: max-content max-content 1fr;

  margin: 0 auto;
  justify-content: center;
  z-index: 101;

  ${props =>
    reMediaQuery({
      position: [null, null, null, getPosition(props.top, 54), getPosition(props.top, 71)],
      top: [null, null, null, getTop(props.top, 54), getTop(props.top, 71)]
    })};

  transition: background-color 200ms linear;
`;

const getIfScrolled = (top: number, limit: number) => {
  return top > limit ? true : false;
};

const getPosition = (top: number, limit: number) => {
  return getIfScrolled(top, limit) ? 'fixed' : 'absolute';
};

const getTop = (top: number, limit: number) => {
  return getIfScrolled(top, limit) ? '0' : `${limit}px`;
};

const getLogoHidden = (top: number, limit: number) => {
  return getIfScrolled(top, limit) ? '0' : '1';
};

const ContentPositionController = styled.div<{isTransparent: boolean}>`
  ${({isTransparent}) =>
    reMediaQuery({
      height: [null, null, null, isTransparent ? 0 : 90, isTransparent ? 0 : 120]
    })};
`;

const DropContainer = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  ${reMediaQuery({
    'margin-right': [null, null, null, '147px', '211px'],
    'padding-top': [null, null, null, 10, 12]
  })}
`;

const PilledDropDowns = styled.div<{
  top?: number;
  screenSize?: string;
  backgroundColor: string;
  borderColor: string;
}>`
  display: flex;
  padding: 10px;
  transition: border 0.3s ease, background-color 0.3s ease, backdrop-filter 0.1s ease;
  ${props => {
    const threshold =
      props.screenSize === breakpoints.laptopL
        ? 71
        : props.screenSize === breakpoints.desktop
        ? 71
        : 54;

    return props.top && props.top > threshold
      ? css`
          border: 1px solid ${props.borderColor};
          background-color: ${props.backgroundColor};
          backdrop-filter: blur(100px);
        `
      : css`
          border: 1px solid transparent;
          background-color: transparent;
          backdrop-filter: none;
        `;
  }}
  ${reMediaQuery({
    'border-Radius': [null, null, null, 10, 16],
    height: [null, null, null, 50, 64],
    'padding-left': [null, null, null, 48, 64],
    'padding-right': [null, null, null, 48, 64]
  })}
`;

const SubContainer = styled.div<{
  paddingLeft?: UnitLessValues | UnitValues | null;
  paddingRight?: UnitLessValues | UnitValues | null;
}>`
  // max-width: 1800px;
  width: 100%;
  display: flex;
  grid-template-columns: max-content max-content 1fr;
`;
